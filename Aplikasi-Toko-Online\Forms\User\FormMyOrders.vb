Imports MySql.Data.MySqlClient
Imports System.Drawing
Imports System.Data

Public Class FormMyOrders
    Private currentUserID As Integer
    
    Public Sub New(userID As Integer)
        InitializeComponent()
        currentUserID = userID
    End Sub
    
    Private Sub FormMyOrders_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "<PERSON><PERSON><PERSON>"
        Me.Size = New Size(1200, 800)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        ' Set form properties untuk tidak bisa di-minimize/maximize
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(1200, 800)
        Me.MaximumSize = New Size(1200, 800)

        CreateUI()
        LoadOrders()
    End Sub
    
    Private Sub CreateUI()
        ' Header Panel dengan gradient dan shadow - POSISI ABSOLUT
        Dim pnlHeader As New Panel With {
            .Size = New Size(Me.Width, 100),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(41, 128, 185),
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }

        ' Tambahkan gradient effect
        AddHandler pnlHeader.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlHeader.Width, pnlHeader.Height)
            Using brush As New Drawing2D.LinearGradientBrush(rect, Color.FromArgb(52, 152, 219), Color.FromArgb(41, 128, 185), Drawing2D.LinearGradientMode.Vertical)
                e.Graphics.FillRectangle(brush, rect)
            End Using
        End Sub

        Dim lblTitle As New Label With {
            .Text = "📦 PESANAN SAYA",
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(40, 30),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)

        ' Filter Panel dengan styling modern - POSISI ABSOLUT DI BAWAH HEADER
        Dim pnlFilter As New Panel With {
            .Size = New Size(Me.Width, 80),
            .Location = New Point(0, 100),
            .BackColor = Color.White,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }

        ' Tambahkan border bawah untuk filter panel
        AddHandler pnlFilter.Paint, Sub(sender, e)
            Using pen As New Pen(Color.FromArgb(230, 230, 230), 1)
                e.Graphics.DrawLine(pen, 0, pnlFilter.Height - 1, pnlFilter.Width, pnlFilter.Height - 1)
            End Using
        End Sub

        Dim lblFilter As New Label With {
            .Text = "🔍 Filter Status:",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(40, 25),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }
        
        Dim cmbStatus As New ComboBox With {
            .Name = "cmbStatus",
            .Size = New Size(180, 35),
            .Location = New Point(180, 22),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 11),
            .FlatStyle = FlatStyle.Flat
        }

        cmbStatus.Items.AddRange({"Semua Status", "pending", "processing", "shipped", "success", "cancelled"})
        cmbStatus.SelectedIndex = 0
        AddHandler cmbStatus.SelectedIndexChanged, AddressOf CmbStatus_SelectedIndexChanged

        Dim btnRefresh As New Button With {
            .Text = "🔄 Refresh",
            .Size = New Size(120, 35),
            .Location = New Point(380, 22),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        AddHandler btnRefresh.Click, AddressOf BtnRefresh_Click
        
        pnlFilter.Controls.AddRange({lblFilter, cmbStatus, btnRefresh})

        ' Orders Panel dengan styling modern - POSISI ABSOLUT
        Dim pnlOrders As New Panel With {
            .Name = "pnlOrders",
            .Location = New Point(0, 180),
            .Size = New Size(Me.Width, Me.Height - 180),
            .BackColor = Color.FromArgb(248, 249, 250),
            .AutoScroll = True,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom,
            .Padding = New Padding(20, 20, 20, 20)
        }

        ' TAMBAHKAN KONTROL DALAM URUTAN YANG BENAR
        Me.Controls.Add(pnlOrders)  ' Tambahkan dulu yang paling bawah
        Me.Controls.Add(pnlFilter)  ' Kemudian filter
        Me.Controls.Add(pnlHeader)  ' Terakhir header (akan di atas)
    End Sub
    
    Private Sub LoadOrders(Optional statusFilter As String = "")
        Try
            koneksi()
            
            Dim pnlOrders As Panel = CType(Me.Controls("pnlOrders"), Panel)
            pnlOrders.Controls.Clear()
            
            Dim query As String = "SELECT o.order_id, o.order_date, o.total_amount, o.order_status, o.shipping_address, " &
                                 "COUNT(od.product_id) as item_count " &
                                 "FROM orders o " &
                                 "LEFT JOIN order_details od ON o.order_id = od.order_id " &
                                 "WHERE o.user_id = @userID"
            
            If statusFilter <> "" AndAlso statusFilter <> "Semua Status" Then
                query &= " AND o.order_status = @status"
            End If
            
            query &= " GROUP BY o.order_id ORDER BY o.order_date DESC"
            
            cmd = New MySqlCommand(query, conn)
            cmd.Parameters.AddWithValue("@userID", currentUserID)
            
            If statusFilter <> "" AndAlso statusFilter <> "Semua Status" Then
                cmd.Parameters.AddWithValue("@status", statusFilter)
            End If
            
            dr = cmd.ExecuteReader()
            
            Dim yPos As Integer = 20
            Dim orderCount As Integer = 0
            
            While dr.Read()
                Dim orderCard As Panel = CreateOrderCard(
                    Convert.ToInt32(dr("order_id")),
                    Convert.ToDateTime(dr("order_date")),
                    Convert.ToDecimal(dr("total_amount")),
                    dr("order_status").ToString(),
                    dr("shipping_address").ToString(),
                    Convert.ToInt32(dr("item_count"))
                )
                
                orderCard.Location = New Point(20, yPos)
                pnlOrders.Controls.Add(orderCard)
                
                yPos += orderCard.Height + 15
                orderCount += 1
            End While
            
            dr.Close()
            
            If orderCount = 0 Then
                Dim lblNoOrders As New Label With {
                    .Text = "Belum ada pesanan",
                    .Font = New Font("Segoe UI", 14),
                    .ForeColor = Color.Gray,
                    .Location = New Point(400, 200),
                    .AutoSize = True,
                    .BackColor = Color.Transparent
                }
                pnlOrders.Controls.Add(lblNoOrders)
            Else
                ' Tambahkan padding di bagian bawah agar scroll tidak terpotong
                Dim paddingPanel As New Panel With {
                    .Size = New Size(1, 50),
                    .Location = New Point(20, yPos),
                    .BackColor = Color.Transparent
                }
                pnlOrders.Controls.Add(paddingPanel)
            End If

            tutupKoneksi()
            
        Catch ex As Exception
            MessageBox.Show("Error loading orders: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function CreateOrderCard(orderID As Integer, orderDate As DateTime, totalAmount As Decimal,
                                   status As String, shippingAddress As String, itemCount As Integer) As Panel

        Dim card As New Panel With {
            .Size = New Size(1120, 180),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .Margin = New Padding(0, 0, 0, 15)
        }

        ' Tambahkan shadow effect dan rounded corners
        AddHandler card.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, card.Width - 1, card.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
            ' Shadow effect
            Using shadowBrush As New SolidBrush(Color.FromArgb(20, 0, 0, 0))
                e.Graphics.FillRectangle(shadowBrush, New Rectangle(2, 2, card.Width - 2, card.Height - 2))
            End Using
        End Sub
        
        ' Order ID dan Date dengan icon - posisi rapi
        Dim lblOrderID As New Label With {
            .Text = $"📦 Pesanan #{orderID}",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(30, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblDate As New Label With {
            .Text = $"📅 {orderDate.ToString("dd MMM yyyy HH:mm")}",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Location = New Point(30, 45),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        ' Status - posisi sejajar dengan order ID
        Dim lblStatus As New Label With {
            .Text = GetStatusText(status),
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .ForeColor = GetStatusColor(status),
            .Location = New Point(920, 15),
            .Size = New Size(120, 25),
            .TextAlign = ContentAlignment.MiddleCenter,
            .BorderStyle = BorderStyle.FixedSingle,
            .BackColor = Color.Transparent
        }

        ' Item Count and Total - posisi rapi
        Dim lblItems As New Label With {
            .Text = $"{itemCount} item(s)",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(30, 75),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblTotal As New Label With {
            .Text = $"Total: Rp {totalAmount:N0}",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(30, 100),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        ' Shipping Address - posisi rapi
        Dim lblAddress As New Label With {
            .Text = $"Alamat: {If(shippingAddress.Length > 50, shippingAddress.Substring(0, 50) & "...", shippingAddress)}",
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.Gray,
            .Location = New Point(30, 130),
            .Size = New Size(500, 20),
            .BackColor = Color.Transparent
        }

        ' Detail Button - posisi sejajar dengan status
        Dim btnDetail As New Button With {
            .Text = "Lihat Detail",
            .Size = New Size(120, 35),
            .Location = New Point(920, 50),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .Tag = orderID
        }
        btnDetail.FlatAppearance.BorderSize = 0
        AddHandler btnDetail.Click, AddressOf BtnDetail_Click

        ' Payment Button - hanya untuk status pending
        Dim controlsToAdd As New List(Of Control) From {lblOrderID, lblDate, lblStatus, lblItems, lblTotal, lblAddress, btnDetail}

        If status.ToLower() = "pending" Then
            Dim btnPay As New Button With {
                .Text = "💳 Bayar",
                .Size = New Size(120, 35),
                .Location = New Point(920, 95),
                .BackColor = Color.FromArgb(46, 204, 113),
                .ForeColor = Color.White,
                .FlatStyle = FlatStyle.Flat,
                .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                .Cursor = Cursors.Hand,
                .Tag = orderID
            }
            btnPay.FlatAppearance.BorderSize = 0
            AddHandler btnPay.Click, AddressOf BtnPay_Click

            controlsToAdd.Add(btnPay)
        End If

        card.Controls.AddRange(controlsToAdd.ToArray())
        
        Return card
    End Function
    
    Private Function GetStatusText(status As String) As String
        Select Case status.ToLower()
            Case "pending"
                Return "Menunggu"
            Case "processing"
                Return "Diproses"
            Case "shipped"
                Return "Dikirim"
            Case "success"
                Return "Berhasil"
            Case "cancelled"
                Return "Dibatalkan"
            Case Else
                Return status
        End Select
    End Function
    
    Private Function GetStatusColor(status As String) As Color
        Select Case status.ToLower()
            Case "pending"
                Return Color.FromArgb(241, 196, 15)
            Case "processing"
                Return Color.FromArgb(52, 152, 219)
            Case "shipped"
                Return Color.FromArgb(155, 89, 182)
            Case "success"
                Return Color.FromArgb(46, 204, 113)
            Case "cancelled"
                Return Color.FromArgb(231, 76, 60)
            Case Else
                Return Color.Gray
        End Select
    End Function
    
    Private Sub CmbStatus_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim cmb As ComboBox = CType(sender, ComboBox)
        LoadOrders(cmb.SelectedItem.ToString())
    End Sub
    
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        Dim cmbStatus As ComboBox = CType(Me.Controls.Find("cmbStatus", True).FirstOrDefault(), ComboBox)
        LoadOrders(cmbStatus.SelectedItem.ToString())
    End Sub
    
    Private Sub BtnDetail_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim orderID As Integer = Convert.ToInt32(btn.Tag)

        Dim frmDetail As New FormOrderDetail(orderID)
        frmDetail.ShowDialog()
    End Sub

    Private Sub BtnPay_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim orderID As Integer = Convert.ToInt32(btn.Tag)

        Try
            ' Get order details untuk pembayaran
            koneksi()
            cmd = New MySqlCommand("SELECT total_amount, payment_method FROM orders WHERE order_id = @orderID AND order_status = 'pending'", conn)
            cmd.Parameters.AddWithValue("@orderID", orderID)

            dr = cmd.ExecuteReader()

            If dr.Read() Then
                Dim totalAmount As Decimal = Convert.ToDecimal(dr("total_amount"))
                Dim paymentMethod As String = dr("payment_method").ToString()
                dr.Close()
                tutupKoneksi()

                ' Konfirmasi pembayaran
                Dim confirmMsg As String = $"Lanjut ke pembayaran Order #{orderID}?" & vbCrLf &
                                          $"Total: Rp {totalAmount:N0}" & vbCrLf &
                                          $"Metode: {paymentMethod}"

                If MessageBox.Show(confirmMsg, "Konfirmasi Pembayaran",
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then

                    ' Buka form payment gateway
                    Dim frmPayment As New FormPaymentGateway(orderID, paymentMethod, totalAmount)
                    Dim paymentResult As DialogResult = frmPayment.ShowDialog()

                    If paymentResult = DialogResult.OK Then
                        MessageBox.Show("✅ Pembayaran berhasil!" & vbCrLf & "Pesanan Anda telah selesai.",
                                       "Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    ElseIf paymentResult = DialogResult.Cancel Then
                        ' User membatalkan atau pembayaran gagal
                        MessageBox.Show("💡 Pembayaran belum selesai." & vbCrLf &
                                       "Pesanan masih tersimpan dan bisa dibayar kapan saja melalui tombol 'Bayar'.",
                                       "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    End If

                    ' Refresh orders list dalam semua kasus
                    Dim cmbStatus As ComboBox = CType(Me.Controls.Find("cmbStatus", True).FirstOrDefault(), ComboBox)
                    LoadOrders(cmbStatus.SelectedItem.ToString())
                End If
            Else
                dr.Close()
                tutupKoneksi()
                MessageBox.Show("Pesanan tidak ditemukan atau sudah tidak bisa dibayar!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            If conn.State = ConnectionState.Open Then
                tutupKoneksi()
            End If
        End Try
    End Sub
End Class
