Imports MySql.Data.MySqlClient
Imports System.Threading

Public Class OrderStatusManager
    Private Shared _instance As OrderStatusManager
    Private timer As Timer
    
    Public Shared ReadOnly Property Instance() As OrderStatusManager
        Get
            If _instance Is Nothing Then
                _instance = New OrderStatusManager()
            End If
            Return _instance
        End Get
    End Property
    
    Private Sub New()
        ' Timer untuk auto-update status setiap 30 detik
        timer = New Timer(AddressOf AutoUpdateOrderStatus, Nothing, TimeSpan.Zero, TimeSpan.FromSeconds(30))
    End Sub
    
    ' Method untuk auto-update status pesanan berdasarkan kondisi
    Private Sub AutoUpdateOrderStatus(state As Object)
        Try
            koneksi()
            
            ' 1. Update status dari processing ke shipped (setelah 2 menit untuk demo)
            Dim queryProcessingToShipped As String = 
                "UPDATE orders SET order_status = 'shipped', updated_at = NOW() " &
                "WHERE order_status = 'processing' " &
                "AND TIMESTAMPDIFF(MINUTE, updated_at, NOW()) >= 2"
            
            cmd = New MySqlCommand(queryProcessingToShipped, conn)
            Dim processedToShipped As Integer = cmd.ExecuteNonQuery()
            
            ' 2. Update status dari shipped ke success (setelah 5 menit untuk demo)
            Dim queryShippedToSuccess As String =
                "UPDATE orders SET order_status = 'success', updated_at = NOW() " &
                "WHERE order_status = 'shipped' " &
                "AND TIMESTAMPDIFF(MINUTE, updated_at, NOW()) >= 5"

            cmd = New MySqlCommand(queryShippedToSuccess, conn)
            Dim shippedToSuccess As Integer = cmd.ExecuteNonQuery()
            
            ' 3. Auto-cancel pending orders setelah 24 jam (untuk demo: 10 menit)
            Dim queryAutoCancelPending As String = 
                "UPDATE orders SET order_status = 'cancelled', updated_at = NOW() " &
                "WHERE order_status = 'pending' " &
                "AND TIMESTAMPDIFF(MINUTE, order_date, NOW()) >= 10"
            
            cmd = New MySqlCommand(queryAutoCancelPending, conn)
            Dim autoCancelled As Integer = cmd.ExecuteNonQuery()
            
            tutupKoneksi()
            
            ' Log aktivitas jika ada perubahan
            If processedToShipped > 0 OrElse shippedToSuccess > 0 OrElse autoCancelled > 0 Then
                Console.WriteLine($"Auto-update: {processedToShipped} processing→shipped, {shippedToSuccess} shipped→success, {autoCancelled} auto-cancelled")
            End If
            
        Catch ex As Exception
            Console.WriteLine($"Error in auto-update: {ex.Message}")
        End Try
    End Sub
    
    ' Method untuk manual update status berdasarkan kondisi user
    Public Sub UpdateOrderStatusBasedOnUserAction(orderID As Integer, userAction As String)
        Try
            koneksi()

            Dim newStatus As String = ""

            Select Case userAction.ToLower()
                Case "payment_success"
                    newStatus = "success"  
                Case "payment_processing"
                    newStatus = "processing"
                Case "payment_failed"
                    ' Status tetap pending
                    Return
                Case "user_cancel"
                    newStatus = "cancelled"
                Case "confirm_delivery"
                    newStatus = "success"
            End Select

            If newStatus <> "" Then
                ' Debug: Cek status sebelum update
                Dim checkCmd As New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", conn)
                checkCmd.Parameters.AddWithValue("@id", orderID)
                Dim currentStatus As Object = checkCmd.ExecuteScalar()

                Console.WriteLine($"DEBUG: Order {orderID} - Status sebelum: {currentStatus}, akan diubah ke: {newStatus}")

                ' Check if updated_at column exists
                Dim checkColumnCmd As New MySqlCommand("SHOW COLUMNS FROM orders LIKE 'updated_at'", conn)
                Dim columnExists As Boolean = False

                Dim columnReader As MySqlDataReader = checkColumnCmd.ExecuteReader()
                If columnReader.Read() Then
                    columnExists = True
                End If
                columnReader.Close()

                ' Update order status with or without updated_at
                Dim updateQuery As String
                If columnExists Then
                    updateQuery = "UPDATE orders SET order_status = @status, updated_at = NOW() WHERE order_id = @id"
                Else
                    updateQuery = "UPDATE orders SET order_status = @status WHERE order_id = @id"
                End If

                cmd = New MySqlCommand(updateQuery, conn)
                cmd.Parameters.AddWithValue("@status", newStatus)
                cmd.Parameters.AddWithValue("@id", orderID)
                Dim rowsAffected As Integer = cmd.ExecuteNonQuery()

                Console.WriteLine($"DEBUG: Rows affected: {rowsAffected}")

                ' Verify update
                Dim verifyCmd As New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", conn)
                verifyCmd.Parameters.AddWithValue("@id", orderID)
                Dim updatedStatus As Object = verifyCmd.ExecuteScalar()

                Console.WriteLine($"DEBUG: Status setelah update: {updatedStatus}")

                ' Log perubahan status
                LogStatusChange(orderID, newStatus, userAction)

                ' Show success message
                MessageBox.Show($"Status order #{orderID} berhasil diubah dari '{currentStatus}' ke '{newStatus}'",
                               "Update Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            tutupKoneksi()

        Catch ex As Exception
            Console.WriteLine($"Error updating order status: {ex.Message}")
            MessageBox.Show($"Error updating order status: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            tutupKoneksi()
        End Try
    End Sub
    
    ' Method untuk cek apakah pesanan bisa dibayar
    Public Function CanOrderBePaid(orderID As Integer) As Boolean
        Try
            koneksi()
            
            cmd = New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", conn)
            cmd.Parameters.AddWithValue("@id", orderID)
            
            Dim status As Object = cmd.ExecuteScalar()
            tutupKoneksi()
            
            If status IsNot Nothing Then
                Return status.ToString().ToLower() = "pending"
            End If
            
            Return False
            
        Catch ex As Exception
            Return False
        End Try
    End Function
    
    ' Method untuk mendapatkan status pesanan
    Public Function GetOrderStatus(orderID As Integer) As String
        Try
            koneksi()
            
            cmd = New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", conn)
            cmd.Parameters.AddWithValue("@id", orderID)
            
            Dim status As Object = cmd.ExecuteScalar()
            tutupKoneksi()
            
            If status IsNot Nothing Then
                Return status.ToString()
            End If
            
            Return "unknown"
            
        Catch ex As Exception
            Return "error"
        End Try
    End Function
    
    ' Method untuk log perubahan status
    Private Sub LogStatusChange(orderID As Integer, newStatus As String, reason As String)
        Try
            cmd = New MySqlCommand("INSERT INTO status_logs (order_id, old_status, new_status, change_reason, change_date) " &
                                  "VALUES (@orderID, (SELECT order_status FROM orders WHERE order_id = @orderID), @newStatus, @reason, NOW())", conn)
            cmd.Parameters.AddWithValue("@orderID", orderID)
            cmd.Parameters.AddWithValue("@newStatus", newStatus)
            cmd.Parameters.AddWithValue("@reason", reason)
            
            Try
                cmd.ExecuteNonQuery()
            Catch
                ' Ignore log error jika tabel tidak ada
            End Try
            
        Catch ex As Exception
            ' Ignore logging errors
        End Try
    End Sub
    
    ' Method untuk mendapatkan estimasi waktu status berikutnya
    Public Function GetEstimatedNextStatusTime(orderID As Integer) As String
        Try
            koneksi()
            
            cmd = New MySqlCommand("SELECT order_status, updated_at FROM orders WHERE order_id = @id", conn)
            cmd.Parameters.AddWithValue("@id", orderID)
            
            Dim reader As MySqlDataReader = cmd.ExecuteReader()
            
            If reader.Read() Then
                Dim status As String = reader("order_status").ToString()
                Dim updatedAt As DateTime = Convert.ToDateTime(reader("updated_at"))
                reader.Close()
                tutupKoneksi()
                
                Select Case status.ToLower()
                    Case "pending"
                        Return "Menunggu pembayaran"
                    Case "processing"
                        Dim estimatedShipping As DateTime = updatedAt.AddMinutes(2)
                        If DateTime.Now < estimatedShipping Then
                            Dim remaining As TimeSpan = estimatedShipping - DateTime.Now
                            Return $"Akan dikirim dalam {Math.Ceiling(remaining.TotalMinutes)} menit"
                        Else
                            Return "Sedang dikirim"
                        End If
                    Case "shipped"
                        Dim estimatedDelivery As DateTime = updatedAt.AddMinutes(5)
                        If DateTime.Now < estimatedDelivery Then
                            Dim remaining As TimeSpan = estimatedDelivery - DateTime.Now
                            Return $"Estimasi tiba dalam {Math.Ceiling(remaining.TotalMinutes)} menit"
                        Else
                            Return "Akan segera tiba"
                        End If
                    Case "success"
                        Return "Pesanan selesai"
                    Case "cancelled"
                        Return "Pesanan dibatalkan"
                    Case Else
                        Return "Status tidak diketahui"
                End Select
            End If
            
            reader.Close()
            tutupKoneksi()
            Return "Tidak dapat memperkirakan"
            
        Catch ex As Exception
            Return "Error mendapatkan estimasi"
        End Try
    End Function
    
    ' Method untuk cleanup resources
    Public Sub Dispose()
        If timer IsNot Nothing Then
            timer.Dispose()
            timer = Nothing
        End If
    End Sub
End Class
