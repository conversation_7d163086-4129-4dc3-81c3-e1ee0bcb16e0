﻿' FormDeleteProduct.vb - Form Konfirmasi Hapus Produk
Imports MySql.Data.MySqlClient
Imports System.IO

Public Class FormDeleteProduct
    Private productID As Integer
    Private Shadows productName As String

    Public Sub New(prodID As Integer, prodName As String)
        InitializeComponent()
        productID = prodID
        productName = prodName
    End Sub

    Private Sub FormDeleteProduct_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Hapus Produk"
        Me.Size = New Size(450, 350)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.BackColor = Color.White

        CreateUI()
        LoadProductInfo()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.FromArgb(231, 76, 60)
        }

        Dim lblTitle As New Label With {
            .Text = "WARNING - KONFIRMASI HAPUS",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 15),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)

        ' Main Panel
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(30)
        }

        ' Warning Icon
        Dim lblWarning As New Label With {
            .Text = "!",
            .Font = New Font("Segoe UI", 48, FontStyle.Bold),
            .ForeColor = Color.FromArgb(231, 76, 60),
            .Size = New Size(100, 80),
            .Location = New Point(160, 20),
            .TextAlign = ContentAlignment.MiddleCenter
        }

        ' Message
        Dim lblMessage As New Label With {
            .Text = "Apakah Anda yakin ingin menghapus produk ini?",
            .Font = New Font("Segoe UI", 12),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(30, 110),
            .Size = New Size(360, 25),
            .TextAlign = ContentAlignment.MiddleCenter
        }

        ' Product Name
        Dim lblProductName As New Label With {
            .Name = "lblProductName",
            .Text = $"""{productName}""",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(44, 62, 80),
            .Location = New Point(30, 140),
            .Size = New Size(360, 30),
            .TextAlign = ContentAlignment.MiddleCenter
        }
        
        ' Additional Info
        Dim lblInfo As New Label With {
            .Name = "lblInfo",
            .Text = "Loading...",
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Location = New Point(30, 175),
            .Size = New Size(360, 50),
            .TextAlign = ContentAlignment.TopCenter
        }
        
        ' Buttons
        Dim btnDelete As New Button With {
            .Text = "DELETE",
            .Size = New Size(150, 45),
            .Location = New Point(60, 240),
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnDelete.FlatAppearance.BorderSize = 0
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click
        
        Dim btnCancel As New Button With {
            .Text = "BATAL",
            .Size = New Size(150, 45),
            .Location = New Point(230, 240),
            .BackColor = Color.FromArgb(149, 165, 166),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()
        
        pnlMain.Controls.AddRange({lblWarning, lblMessage, lblProductName, lblInfo, btnDelete, btnCancel})
        
        Me.Controls.AddRange({pnlMain, pnlHeader})
    End Sub
    
    Private Sub LoadProductInfo()
        Try
            koneksi()
            
            ' Check if product is used in orders
            cmd = New MySqlCommand("Select COUNT(*) FROM order_details WHERE product_id = @id", conn)
            cmd.Parameters.AddWithValue("@id", productID)
            Dim orderCount As Integer = Convert.ToInt32(cmd.ExecuteScalar())
            
            ' Get product details
            cmd = New MySqlCommand("Select stock, image_url FROM products WHERE product_id = @id", conn)
            cmd.Parameters.AddWithValue("@id", productID)
            dr = cmd.ExecuteReader()
            
            Dim stock As Integer = 0
            Dim imageUrl As String = ""
            
            If dr.Read() Then
                stock = Convert.ToInt32(dr("stock"))
                imageUrl = dr("image_url").ToString()
            End If
            
            dr.Close()
            
            ' Count wishlist
            cmd = New MySqlCommand("Select COUNT(*) FROM wishlist WHERE product_id = @id", conn)
            cmd.Parameters.AddWithValue("@id", productID)
            Dim wishlistCount As Integer = Convert.ToInt32(cmd.ExecuteScalar())
            
            tutupKoneksi()
            
            ' Update info label
            Dim lblInfo As Label = CType(Me.Controls.Find("lblInfo", True).FirstOrDefault(), Label)
            
            Dim infoText As String = $"Stok {stock} unit" & vbCrLf
            
            If orderCount > 0 Then
                infoText &= $"WARNING: Produk ini sudah digunakan dalam {orderCount} transaksi!" & vbCrLf
                lblInfo.ForeColor = Color.FromArgb(231, 76, 60)
            End If
            
            If wishlistCount > 0 Then
                infoText &= $"WISHLIST: Ada di {wishlistCount} wishlist user"
            End If
            
            lblInfo.Text = infoText
            
        Catch ex As Exception
            MessageBox.Show("Error loading product info " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        Try
            koneksi()
            
            ' Start transaction
            Dim trans As MySqlTransaction = conn.BeginTransaction()
            
            Try
                ' Get image path before delete
                cmd = New MySqlCommand("SELECT image_url FROM products WHERE product_id = @id", conn, trans)
                cmd.Parameters.AddWithValue("@id", productID)
                Dim imagePath As String = Convert.ToString(cmd.ExecuteScalar())
                
                ' Check if used in orders
                cmd = New MySqlCommand("SELECT COUNT(*) FROM order_details WHERE product_id = @id", conn, trans)
                cmd.Parameters.AddWithValue("@id", productID)
                Dim orderCount As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                
                If orderCount > 0 Then
                    MessageBox.Show("Produk tidak dapat dihapus karena sudah digunakan dalam transaksi!" & vbCrLf & 
                                  "Anda dapat mengubah stok menjadi 0 untuk menonaktifkan produk.", 
                                  "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    trans.Rollback()
                    tutupKoneksi()
                    Return
                End If
                
                ' Delete from wishlist first
                cmd = New MySqlCommand("DELETE FROM wishlist WHERE product_id = @id", conn, trans)
                cmd.Parameters.AddWithValue("@id", productID)
                cmd.ExecuteNonQuery()
                
                ' Delete product
                cmd = New MySqlCommand("DELETE FROM products WHERE product_id = @id", conn, trans)
                cmd.Parameters.AddWithValue("@id", productID)
                cmd.ExecuteNonQuery()
                
                ' Commit transaction
                trans.Commit()
                
                ' Delete image file if exists
                If imagePath <> "" AndAlso File.Exists(imagePath) Then
                    Try
                        File.Delete(imagePath)
                    Catch
                        ' Ignore if cannot delete file
                    End Try
                End If
                
                tutupKoneksi()
                
                MessageBox.Show($"Produk '{productName}' berhasil dihapus!", "Sukses",
                              MessageBoxButtons.OK, MessageBoxIcon.Information)

                Me.DialogResult = DialogResult.OK
                Me.Close()

            Catch ex As Exception
                trans.Rollback()
                Throw ex
            End Try

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class