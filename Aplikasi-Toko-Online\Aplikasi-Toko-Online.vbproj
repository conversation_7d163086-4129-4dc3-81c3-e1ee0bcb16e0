﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FA82854D-C544-43B1-AAAC-9A3FC8B320FC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Aplikasi_Toko_Online.My.MyApplication</StartupObject>
    <RootNamespace>Aplikasi_Toko_Online</RootNamespace>
    <AssemblyName>Aplikasi-Toko-Online</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>Aplikasi-Toko-Online.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>Aplikasi-Toko-Online.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="MySql.Data, Version=6.9.8.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>C:\Program Files (x86)\MySQL\MySQL Connector Net 6.9.8\Assemblies\v4.5\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <!-- Admin Forms -->
    <Compile Include="Forms\Admin\FormAddProduct.Designer.vb">
      <DependentUpon>FormAddProduct.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Admin\FormAddProduct.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminDashboard.Designer.vb">
      <DependentUpon>FormAdminDashboard.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminDashboard.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminOrders.Designer.vb">
      <DependentUpon>FormAdminOrders.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminOrders.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminProducts.Designer.vb">
      <DependentUpon>FormAdminProducts.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminProducts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminUsers.Designer.vb">
      <DependentUpon>FormAdminUsers.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Admin\FormAdminUsers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Admin\FormDeleteProduct.Designer.vb">
      <DependentUpon>FormDeleteProduct.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Admin\FormDeleteProduct.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Admin\FormEditProduct.Designer.vb">
      <DependentUpon>FormEditProduct.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Admin\FormEditProduct.vb">
      <SubType>Form</SubType>
    </Compile>

    <!-- User Forms -->
    <Compile Include="Forms\User\FormCart.Designer.vb">
      <DependentUpon>FormCart.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormCart.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormCheckout.Designer.vb">
      <DependentUpon>FormCheckout.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormCheckout.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormMain.Designer.vb">
      <DependentUpon>FormMain.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormMyOrders.Designer.vb">
      <DependentUpon>FormMyOrders.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormMyOrders.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormOrderDetail.Designer.vb">
      <DependentUpon>FormOrderDetail.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormOrderDetail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormPaymentGateway.Designer.vb">
      <DependentUpon>FormPaymentGateway.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormPaymentGateway.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormPaymentHistory.Designer.vb">
      <DependentUpon>FormPaymentHistory.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormPaymentHistory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormViewProducts.Designer.vb">
      <DependentUpon>FormViewProducts.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormViewProducts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\User\FormWishlist.Designer.vb">
      <DependentUpon>FormWishlist.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\User\FormWishlist.vb">
      <SubType>Form</SubType>
    </Compile>

    <!-- Auth Forms -->
    <Compile Include="Forms\Auth\FormLogin.Designer.vb">
      <DependentUpon>FormLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Auth\FormLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Auth\FormRegister.Designer.vb">
      <DependentUpon>FormRegister.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Auth\FormRegister.vb">
      <SubType>Form</SubType>
    </Compile>

    <!-- Modules -->
    <Compile Include="Modules\ModulKoneksi.Designer.vb">
      <DependentUpon>ModulKoneksi.vb</DependentUpon>
    </Compile>
    <Compile Include="Modules\ModulKoneksi.vb" />
    <Compile Include="Modules\OrderStatusManager.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <!-- Admin Resources -->
    <EmbeddedResource Include="Forms\Admin\FormAddProduct.resx">
      <DependentUpon>FormAddProduct.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Admin\FormAdminDashboard.resx">
      <DependentUpon>FormAdminDashboard.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Admin\FormDeleteProduct.resx">
      <DependentUpon>FormDeleteProduct.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Admin\FormEditProduct.resx">
      <DependentUpon>FormEditProduct.vb</DependentUpon>
    </EmbeddedResource>

    <!-- Auth Resources -->
    <EmbeddedResource Include="Forms\Auth\FormLogin.resx">
      <DependentUpon>FormLogin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Auth\FormRegister.resx">
      <DependentUpon>FormRegister.vb</DependentUpon>
    </EmbeddedResource>

    <!-- User Resources -->
    <EmbeddedResource Include="Forms\User\FormMain.resx">
      <DependentUpon>FormMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\User\FormViewProducts.resx">
      <DependentUpon>FormViewProducts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\User\FormWishlist.resx">
      <DependentUpon>FormWishlist.vb</DependentUpon>
    </EmbeddedResource>

    <!-- Module Resources -->
    <EmbeddedResource Include="Modules\ModulKoneksi.resx">
      <DependentUpon>ModulKoneksi.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>