' FormAdminProducts.vb - Form khusus admin untuk pengelolaan produk
Imports MySql.Data.MySqlClient

Public Class FormAdminProducts
    Private Sub FormAdminProducts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Admin - Kelola Produk"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        CreateUI()
        LoadProducts()
        LoadCategories()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 80,
            .BackColor = Color.FromArgb(52, 58, 64)
        }

        Dim lblTitle As New Label With {
            .Text = "KELOLA PRODUK",
            .Font = New Font("Segoe UI", 20, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 25),
            .AutoSize = True
        }

        Dim btnClose As New Button With {
            .Text = "✕",
            .Size = New Size(40, 40),
            .Location = New Point(Me.Width - 60, 20),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(220, 53, 69),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnClose.FlatAppearance.BorderSize = 0
        AddHandler btnClose.Click, Sub() Me.Close()

        pnlHeader.Controls.AddRange({lblTitle, btnClose})

        ' Toolbar Panel
        Dim pnlToolbar As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.White,
            .Padding = New Padding(20, 10, 20, 10)
        }

        ' Search controls
        Dim lblSearch As New Label With {
            .Text = "Cari:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 18),
            .AutoSize = True
        }

        Dim txtSearch As New TextBox With {
            .Name = "txtSearch",
            .Size = New Size(200, 25),
            .Location = New Point(60, 15),
            .Font = New Font("Segoe UI", 10)
        }
        AddHandler txtSearch.TextChanged, AddressOf TxtSearch_TextChanged

        ' Category filter
        Dim lblCategory As New Label With {
            .Text = "Kategori:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(280, 18),
            .AutoSize = True
        }

        Dim cmbCategory As New ComboBox With {
            .Name = "cmbCategory",
            .Size = New Size(150, 25),
            .Location = New Point(340, 15),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 10)
        }
        AddHandler cmbCategory.SelectedIndexChanged, AddressOf CmbCategory_SelectedIndexChanged

        ' Action buttons
        Dim btnAdd As New Button With {
            .Text = "➕ TAMBAH PRODUK",
            .Size = New Size(140, 35),
            .Location = New Point(520, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(40, 167, 69),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnAdd.FlatAppearance.BorderSize = 0
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click

        Dim btnEdit As New Button With {
            .Text = "✏️ EDIT",
            .Size = New Size(80, 35),
            .Location = New Point(670, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(255, 193, 7),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnEdit.FlatAppearance.BorderSize = 0
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click

        Dim btnDelete As New Button With {
            .Text = "🗑️ HAPUS",
            .Size = New Size(80, 35),
            .Location = New Point(760, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(220, 53, 69),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnDelete.FlatAppearance.BorderSize = 0
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click

        Dim btnRefresh As New Button With {
            .Text = "🔄 REFRESH",
            .Size = New Size(100, 35),
            .Location = New Point(850, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        AddHandler btnRefresh.Click, Sub() LoadProducts()

        pnlToolbar.Controls.AddRange({lblSearch, txtSearch, lblCategory, cmbCategory, btnAdd, btnEdit, btnDelete, btnRefresh})

        ' Main Panel for DataGridView
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(20)
        }

        ' DataGridView
        Dim dgvProducts As New DataGridView With {
            .Name = "dgvProducts",
            .Dock = DockStyle.Fill,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .ReadOnly = True,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .RowHeadersVisible = False,
            .Font = New Font("Segoe UI", 10),
            .RowTemplate = New DataGridViewRow With {.Height = 40}
        }

        ' Style the DataGridView
        dgvProducts.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgvProducts.DefaultCellStyle.SelectionForeColor = Color.White
        dgvProducts.DefaultCellStyle.Padding = New Padding(5)
        dgvProducts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64)
        dgvProducts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgvProducts.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        dgvProducts.ColumnHeadersHeight = 45
        dgvProducts.EnableHeadersVisualStyles = False

        AddHandler dgvProducts.CellDoubleClick, AddressOf DgvProducts_CellDoubleClick

        pnlMain.Controls.Add(dgvProducts)

        ' Status Panel
        Dim pnlStatus As New Panel With {
            .Dock = DockStyle.Bottom,
            .Height = 30,
            .BackColor = Color.FromArgb(248, 249, 250)
        }

        Dim lblStatus As New Label With {
            .Name = "lblStatus",
            .Text = "Siap",
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Location = New Point(20, 8),
            .AutoSize = True
        }

        pnlStatus.Controls.Add(lblStatus)

        Me.Controls.AddRange({pnlMain, pnlStatus, pnlToolbar, pnlHeader})
    End Sub

    Private Sub LoadProducts(Optional searchText As String = "", Optional categoryFilter As String = "")
        Try
            koneksi()

            Dim query As String = "SELECT p.product_id as 'ID', p.product_name as 'Nama Produk', " &
                                 "c.category_name as 'Kategori', p.price as 'Harga', p.stock as 'Stok', " &
                                 "p.description as 'Deskripsi', p.image_url as 'Gambar' " &
                                 "FROM products p JOIN categories c ON p.category_id = c.category_id WHERE 1=1 "

            If searchText <> "" Then
                query &= "AND p.product_name LIKE '%" & searchText & "%' "
            End If

            If categoryFilter <> "" AndAlso categoryFilter <> "Semua Kategori" Then
                query &= "AND c.category_name = '" & categoryFilter & "' "
            End If

            query &= "ORDER BY p.product_id DESC"

            cmd = New MySqlCommand(query, conn)
            Dim adapter As New MySqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)

            Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)
            If dgv IsNot Nothing Then
                dgv.DataSource = dt

                ' Format columns
                If dgv.Columns.Contains("Harga") Then
                    dgv.Columns("Harga").DefaultCellStyle.Format = "C0"
                    dgv.Columns("Harga").DefaultCellStyle.FormatProvider = New System.Globalization.CultureInfo("id-ID")
                End If

                If dgv.Columns.Contains("Gambar") Then
                    dgv.Columns("Gambar").Visible = False
                End If

                If dgv.Columns.Contains("ID") Then
                    dgv.Columns("ID").Width = 50
                End If
            End If

            ' Update status
            Dim lblStatus As Label = CType(Me.Controls.Find("lblStatus", True).FirstOrDefault(), Label)
            If lblStatus IsNot Nothing Then
                lblStatus.Text = $"Total: {dt.Rows.Count} produk"
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading products: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCategories()
        Try
            koneksi()

            Dim cmbCategory As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
            If cmbCategory IsNot Nothing Then
                cmbCategory.Items.Clear()
                cmbCategory.Items.Add("Semua Kategori")

                cmd = New MySqlCommand("SELECT category_name FROM categories ORDER BY category_name", conn)
                Dim reader As MySqlDataReader = cmd.ExecuteReader()

                While reader.Read()
                    cmbCategory.Items.Add(reader("category_name").ToString())
                End While

                reader.Close()
                cmbCategory.SelectedIndex = 0
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading categories: " & ex.Message)
        End Try
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs)
        Dim searchText As String = CType(sender, TextBox).Text.Trim()
        Dim cmbCategory As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
        Dim categoryFilter As String = If(cmbCategory IsNot Nothing, cmbCategory.Text, "")
        LoadProducts(searchText, categoryFilter)
    End Sub

    Private Sub CmbCategory_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim categoryFilter As String = CType(sender, ComboBox).Text
        Dim txtSearch As TextBox = CType(Me.Controls.Find("txtSearch", True).FirstOrDefault(), TextBox)
        Dim searchText As String = If(txtSearch IsNot Nothing, txtSearch.Text.Trim(), "")
        LoadProducts(searchText, categoryFilter)
    End Sub

    Private Sub DgvProducts_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        Dim formAdd As New FormAddProduct()
        If formAdd.ShowDialog() = DialogResult.OK Then
            LoadProducts()
        End If
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih produk yang akan diedit!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim productID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("ID").Value)
        Dim formEdit As New FormEditProduct(productID)
        If formEdit.ShowDialog() = DialogResult.OK Then
            LoadProducts()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih produk yang akan dihapus!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim productID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("ID").Value)
        Dim productName As String = dgv.SelectedRows(0).Cells("Nama Produk").Value.ToString()

        If MessageBox.Show($"Yakin ingin menghapus produk '{productName}'?", "Konfirmasi Hapus", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                koneksi()
                cmd = New MySqlCommand("DELETE FROM products WHERE product_id = @id", conn)
                cmd.Parameters.AddWithValue("@id", productID)
                cmd.ExecuteNonQuery()
                tutupKoneksi()

                MessageBox.Show("Produk berhasil dihapus!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadProducts()

            Catch ex As Exception
                MessageBox.Show("Error menghapus produk: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class
