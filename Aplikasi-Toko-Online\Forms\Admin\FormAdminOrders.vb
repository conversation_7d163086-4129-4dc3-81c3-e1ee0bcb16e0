' FormAdminOrders.vb - Form khusus admin untuk mengelola pesanan
Imports MySql.Data.MySqlClient

Public Class FormAdminOrders
    Private Sub FormAdminOrders_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Admin - Ke<PERSON>la <PERSON>"
        Me.Size = New Size(1300, 750)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        CreateUI()
        LoadOrders()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 80,
            .BackColor = Color.FromArgb(52, 58, 64)
        }

        Dim lblTitle As New Label With {
            .Text = "KELOLA PESANAN",
            .Font = New Font("Segoe UI", 20, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 25),
            .AutoSize = True
        }

        Dim btnClose As New Button With {
            .Text = "✕",
            .Size = New Size(40, 40),
            .Location = New Point(Me.Width - 60, 20),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(220, 53, 69),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnClose.FlatAppearance.BorderSize = 0
        AddHandler btnClose.Click, Sub() Me.Close()

        pnlHeader.Controls.AddRange({lblTitle, btnClose})

        ' Toolbar Panel
        Dim pnlToolbar As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.White,
            .Padding = New Padding(20, 10, 20, 10)
        }

        ' Search controls
        Dim lblSearch As New Label With {
            .Text = "Cari Order ID:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 18),
            .AutoSize = True
        }

        Dim txtSearch As New TextBox With {
            .Name = "txtSearch",
            .Size = New Size(150, 25),
            .Location = New Point(120, 15),
            .Font = New Font("Segoe UI", 10)
        }
        AddHandler txtSearch.TextChanged, AddressOf TxtSearch_TextChanged

        ' Delete order button
        Dim btnDeleteOrder As New Button With {
            .Text = "🗑️ HAPUS PESANAN",
            .Size = New Size(160, 35),
            .Location = New Point(290, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnDeleteOrder.FlatAppearance.BorderSize = 0
        AddHandler btnDeleteOrder.Click, AddressOf BtnDeleteOrder_Click

        Dim btnViewDetail As New Button With {
            .Text = "👁️ DETAIL",
            .Size = New Size(100, 35),
            .Location = New Point(460, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnViewDetail.FlatAppearance.BorderSize = 0
        AddHandler btnViewDetail.Click, AddressOf BtnViewDetail_Click

        Dim btnRefresh As New Button With {
            .Text = "🔄 REFRESH",
            .Size = New Size(100, 35),
            .Location = New Point(570, 12),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        AddHandler btnRefresh.Click, Sub() LoadOrders()

        pnlToolbar.Controls.AddRange({lblSearch, txtSearch, btnDeleteOrder, btnViewDetail, btnRefresh})

        ' Main Panel for DataGridView
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(20)
        }

        ' DataGridView
        Dim dgvOrders As New DataGridView With {
            .Name = "dgvOrders",
            .Dock = DockStyle.Fill,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .ReadOnly = True,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .RowHeadersVisible = False,
            .Font = New Font("Segoe UI", 10),
            .RowTemplate = New DataGridViewRow With {.Height = 45}
        }

        ' Style the DataGridView
        dgvOrders.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgvOrders.DefaultCellStyle.SelectionForeColor = Color.White
        dgvOrders.DefaultCellStyle.Padding = New Padding(5)
        dgvOrders.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64)
        dgvOrders.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgvOrders.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        dgvOrders.ColumnHeadersHeight = 45
        dgvOrders.EnableHeadersVisualStyles = False

        AddHandler dgvOrders.CellDoubleClick, AddressOf DgvOrders_CellDoubleClick

        pnlMain.Controls.Add(dgvOrders)

        ' Status Panel
        Dim pnlStatus As New Panel With {
            .Dock = DockStyle.Bottom,
            .Height = 30,
            .BackColor = Color.FromArgb(248, 249, 250)
        }

        Dim lblStatusInfo As New Label With {
            .Name = "lblStatusInfo",
            .Text = "Siap",
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Location = New Point(20, 8),
            .AutoSize = True
        }

        pnlStatus.Controls.Add(lblStatusInfo)

        Me.Controls.AddRange({pnlMain, pnlStatus, pnlToolbar, pnlHeader})
    End Sub

    Private Sub LoadOrders(Optional searchText As String = "")
        Try
            koneksi()

            Dim query As String = "SELECT o.order_id as 'Order ID', u.username as 'Customer', " &
                                 "o.order_date as 'Tanggal Order', o.total_amount as 'Total', " &
                                 "o.order_status as 'Status', o.shipping_address as 'Alamat Pengiriman' " &
                                 "FROM orders o JOIN users u ON o.user_id = u.user_id WHERE 1=1 "

            If searchText <> "" Then
                query &= "AND o.order_id LIKE '%" & searchText & "%' "
            End If

            query &= "ORDER BY o.order_date DESC"

            cmd = New MySqlCommand(query, conn)
            Dim adapter As New MySqlDataAdapter(cmd)
            Dim dt As New DataTable()
            adapter.Fill(dt)

            Dim dgv As DataGridView = CType(Me.Controls.Find("dgvOrders", True).FirstOrDefault(), DataGridView)
            If dgv IsNot Nothing Then
                dgv.DataSource = dt

                ' Format columns
                If dgv.Columns.Contains("Total") Then
                    dgv.Columns("Total").DefaultCellStyle.Format = "C0"
                    dgv.Columns("Total").DefaultCellStyle.FormatProvider = New System.Globalization.CultureInfo("id-ID")
                End If

                If dgv.Columns.Contains("Tanggal Order") Then
                    dgv.Columns("Tanggal Order").DefaultCellStyle.Format = "dd/MM/yyyy HH:mm"
                End If

                ' Color code status
                For Each row As DataGridViewRow In dgv.Rows
                    If row.Cells("Status").Value IsNot Nothing Then
                        Dim status As String = row.Cells("Status").Value.ToString().ToLower()
                        Select Case status
                            Case "pending"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(255, 193, 7)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "processing"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(52, 152, 219)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "shipped"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(255, 152, 0)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "delivered"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(40, 167, 69)
                                row.Cells("Status").Style.ForeColor = Color.White
                            Case "cancelled"
                                row.Cells("Status").Style.BackColor = Color.FromArgb(220, 53, 69)
                                row.Cells("Status").Style.ForeColor = Color.White
                        End Select
                    End If
                Next
            End If

            ' Update status
            Dim lblStatus As Label = CType(Me.Controls.Find("lblStatusInfo", True).FirstOrDefault(), Label)
            If lblStatus IsNot Nothing Then
                lblStatus.Text = $"Total: {dt.Rows.Count} pesanan"
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading orders: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs)
        Dim searchText As String = CType(sender, TextBox).Text.Trim()
        LoadOrders(searchText)
    End Sub



    Private Sub DgvOrders_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnViewDetail_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub BtnDeleteOrder_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvOrders", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih pesanan yang ingin dihapus terlebih dahulu!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim selectedRow As DataGridViewRow = dgv.SelectedRows(0)
        Dim orderID As Integer = Convert.ToInt32(selectedRow.Cells("Order ID").Value)
        Dim customerName As String = selectedRow.Cells("Customer").Value.ToString()
        Dim totalAmount As Decimal = Convert.ToDecimal(selectedRow.Cells("Total").Value)

        ' Konfirmasi penghapusan
        Dim result As DialogResult = MessageBox.Show(
            $"Apakah Anda yakin ingin menghapus pesanan berikut?" & vbCrLf & vbCrLf &
            $"Order ID: {orderID}" & vbCrLf &
            $"Customer: {customerName}" & vbCrLf &
            $"Total: Rp {totalAmount:N0}" & vbCrLf & vbCrLf &
            "⚠️ PERINGATAN: Data yang dihapus tidak dapat dikembalikan!",
            "Konfirmasi Hapus Pesanan",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question)

        If result = DialogResult.Yes Then
            Try
                koneksi()

                ' Hapus order details terlebih dahulu (foreign key constraint)
                cmd = New MySqlCommand("DELETE FROM order_details WHERE order_id = @orderID", conn)
                cmd.Parameters.AddWithValue("@orderID", orderID)
                cmd.ExecuteNonQuery()

                ' Hapus order
                cmd = New MySqlCommand("DELETE FROM orders WHERE order_id = @orderID", conn)
                cmd.Parameters.AddWithValue("@orderID", orderID)
                Dim rowsAffected As Integer = cmd.ExecuteNonQuery()

                tutupKoneksi()

                If rowsAffected > 0 Then
                    MessageBox.Show($"Pesanan #{orderID} berhasil dihapus!", "Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadOrders() ' Refresh data
                Else
                    MessageBox.Show("Gagal menghapus pesanan!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If

            Catch ex As Exception
                MessageBox.Show($"Error menghapus pesanan: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                tutupKoneksi()
            End Try
        End If
    End Sub

    Private Sub BtnViewDetail_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvOrders", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih pesanan yang akan dilihat detailnya!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim orderID As String = dgv.SelectedRows(0).Cells("Order ID").Value.ToString()

        ' You can create a detailed order view form here
        ' For now, just show basic info
        MessageBox.Show($"Detail pesanan {orderID} akan ditampilkan di form terpisah", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class
